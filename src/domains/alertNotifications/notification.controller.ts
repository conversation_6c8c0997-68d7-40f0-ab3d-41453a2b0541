// notifications.controller.ts
import { Controller, Get, NotFoundException, Param, Patch, Req } from '@nestjs/common';
import { NotificationModel } from './notification.model'; // Import the NotificationModel
import { Notification } from './notification.interface'; // Assuming you have defined the Notification interface
import prepareDroneDetectionData from './drone-data.service';

@Controller('api/notifications')
export class NotificationsController {
  constructor(private readonly notificationModel: NotificationModel) {} // Inject the NotificationModel

  @Get()
  async findAllForUser(@Req() req: Request): Promise<Notification[]> {
    const user = req['user'];
    const org_ig = req['org_id'];
    const data = await this.notificationModel.findAllForUser(user['_id'], org_ig);
    const result: any[] = [];
    for (let index = 0; index < data.length; index++) {
      result.push(prepareDroneDetectionData(data[index].event[0], data[index].alertZone, data[index].droneAuthId))
    }
    return result;
  }

  @Patch(':id')
  async markAsSeen(@Param('id') id: string): Promise<Notification> {
    const notification = await this.notificationModel.findOne(id);
    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }
    return notification;
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Notification> {
    const notification = await this.notificationModel.findOne(id);
    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }
    return notification;
  }
}
