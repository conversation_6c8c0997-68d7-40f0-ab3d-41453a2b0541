require('dotenv').config();
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EventModel } from 'src/domains/events/event.model';
import EventHistorySchema from 'src/domains/events/eventHistory.schema';
import EventProfileSchema from 'src/domains/events/eventProfile.schema';
import alertZoneSchema from 'src/domains/alertZones/alertZone.schema';
import { NotificationModel } from 'src/domains/alertNotifications/notification.model';
import NotificationSchema from 'src/domains/alertNotifications/notification.schema';
import UserPreferencesSchema from 'src/domains/userPreferences/userPreferences.schema';
import { UserPreferencesModel } from 'src/domains/userPreferences/userPreferences.model';
import { OrganizationModule } from 'src/domains/organizations/organization.module';
import Constants from 'src/common/constants';
import SystemNotificationSchema from 'src/domains/systemNotifications/systemNotification.schema';
import DroneSchema from 'src/domains/drones/drone.schema';
import DroneAuthorizationSchema from 'src/domains/droneAuthorizations/droneAuthorization.schema';
import LogSchema from 'src/domains/logging/log.schema';

@Module({
  imports: [
    MongooseModule.forRoot(process.env.MONGODB_CONNECTION_STRING, {
      dbName: process.env.DATABASE_NAME,
    }),
    MongooseModule.forFeature([
      { name: Constants.event_profile, schema: EventProfileSchema, collection: Constants.event_profile},
      { name: Constants.event_history, schema: EventHistorySchema, collection: Constants.event_history },
      { name: Constants.drones, schema: DroneSchema, collection: Constants.drones },
      { name: Constants.droneAuthorizations, schema: DroneAuthorizationSchema },
      { name: Constants.alertZones, schema: alertZoneSchema },
      { name: Constants.notification, schema: NotificationSchema },
      { name: Constants.system_notification, schema: SystemNotificationSchema },
      { name: Constants.userPreferences, schema: UserPreferencesSchema },
      { name: 'logs', schema: LogSchema },
    ]),
    OrganizationModule,
  ],
  providers: [NotificationModel, UserPreferencesModel],
  exports: [NotificationModel, UserPreferencesModel],
})
export class DatabaseModule {}
